@import url(./fontCss/fontStylesheet.css);

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: "Poppins", serif;
}

.portal-div {
  position: fixed;

  z-index: 150;
}
/* 
html {
  transform: scale(0.75);
  transform-origin: top left;
  min-width: 133.33%;
  min-height: 133.33%;
} */

html, body {
  zoom: 1;
  transform: none;
}


input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

body::first-letter {
  text-transform: uppercase;
}

select option {
  background-color: #4F2683 !important;
  color: #ffffff !important;
}

select option:hover {
  background-color: red !important;
  color: #ffffff !important;
}


.custom-option {
  background-color: #4F2683 !important;
  color: #ffff !important;
}

/* Hover, Focus, and Selected state */
.custom-option:hover,
.custom-option:focus,
.custom-option:checked {
  background-color: red !important;
  color: #ffff !important;
}


.custom-time-picker {
  width: 100%;
  font-size: 18px;
  border: 1px solid #4F2683;
  border-radius: 8px;
  background: white;
  padding: 8px;
}

/* Clock Styling */
.react-time-picker__clock {
  background-color: #4F2683;
  color: white;
  border-radius: 8px;
  padding: 8px;
}

/* Time Text Styling */
.react-time-picker__inputGroup__input {
  color: #d32f2f;
  text-align: center;
}

.react-time-picker__inputGroup__input:hover {
  background: #f5f5f5;
  border-color: #4F2683;
}

.custom-select {
  padding: 8px 12px;
  border: 2px solid #ff0000;
  background-color: white;
  color: #333;
  font-size: 16px;
  border-radius: 6px;
}

.custom-select option {
  background: #ffcccc;
  color: #ff0000;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #F5F5F5;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;

  background-color: #8F8F8F;
}

::-webkit-scrollbar-thumb {
  background-color: #8F8F8F;
}



/* Prevent opacity change on sortable column hover */
.rdt_TableCol:hover {
  opacity: 1 !important;
}

/* Ensure sorted columns don't change opacity */
.rdt_TableCol_Sortable {
  opacity: 1 !important;
}

.no-text-select {
  user-select: none;
}

.hide-number-spin {
  -moz-appearance: textfield;
  /* Remove up/down arrows in Firefox */
}

.hide-number-spin::-webkit-inner-spin-button,
.hide-number-spin::-webkit-outer-spin-button {
  -webkit-appearance: none;
  /* Remove up/down arrows in Chrome/Edge/Safari */
  margin: 0;
}

.sscroll::-webkit-scrollbar {
  width: 0px;
  height: 0px;

  background-color: #8F8F8F;
}

/* ---------- Smooth Sidebar Transition ---------- */
/* .sidebar-container {
  transition: width 300ms ease-in-out;
} */

.menu-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  cursor: pointer;
}

.menu-item:hover {
  background-color: #f3e8ff;
  /* purple-100 */
}

.menu-icon {
  margin-right: 0.5rem;
}

.dropdown-arrow {
  position: absolute;
  right: 1rem;
  transition: transform 0.3s ease;
}

.dropdown-arrow.open {
  transform: rotate(180deg);
}

fieldset legend {
  display: none !important;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

.pulsating-icon {
  animation: pulse 1.5s infinite;
}


.hide-scrollbar::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}


/* Sidebar.css */

/* smooth open/close */
.sidebar-container {
  transition: width 0.4s ease-in-out;
}

/* always‑visible divider */
.divider {
  width: 100%;
  height: 1px;
  background-color: #E5E7EB;
  /* margin: 0.1rem 0; */
}

.modal-transition {
  transition: width 0.6s ease-in-out;
  overflow: hidden; /* optional, for smooth effect */
}