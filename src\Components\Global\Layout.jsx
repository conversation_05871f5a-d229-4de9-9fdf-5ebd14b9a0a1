import React, { useEffect } from 'react';
import Header from './Header/Header';
import Sidebar from './Sidebar/Sidebar';
import { Outlet, useNavigate } from 'react-router-dom';
import api from '../../api';
import { useSelector } from "react-redux";
import { useTranslation } from 'react-i18next';

const Layout = ({ children }) => {
  const navigate = useNavigate();
  const auth = useSelector((state) => state.auth);
  const { t } = useTranslation();

  useEffect(() => {
    const handleUnauthorized = (error) => {
      if (error.isAuthError) {
        navigate("/login");
      }
    };

    const interceptor = api.interceptors.response.use(
      (response) => response,
      (error) => {
        handleUnauthorized(error);
        return Promise.reject(error);
      }
    );

    return () => api.interceptors.response.eject(interceptor);
  }, [navigate]);

  useEffect(() => {
    if (!auth.tokens) {
      navigate('/login');
    }
  }, [auth.tokens, navigate]);

  return (
    <div className="layout">
      <Header title={t('layout.header')} />
      <div className="main mt-14">
        <div>
          <Sidebar title={t('layout.sidebar')} />
        </div>
        <div className="content">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default Layout;
