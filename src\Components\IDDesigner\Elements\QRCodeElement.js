import React, { forwardRef } from 'react';
import { Rect, Text, Group } from 'react-konva';
import { useSelector } from 'react-redux';
import Mustache from 'mustache';

const QRCodeElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const { mockData } = useSelector((state) => state.idDesigner);

  const getQRData = () => {
    if (isPreviewMode && element.data) {
      try {
        return Mustache.render(element.data, mockData);
      } catch (error) {
        console.warn('Error rendering QR data template:', error);
        return element.data;
      }
    }
    return element.data || 'QR Data';
  };

  return (
    <Group
      ref={ref}
      x={element.x}
      y={element.y}
      offsetX={0}
      offsetY={0}
      width={element.width}
      height={element.height}
      rotation={element.rotation || 0}
      opacity={element.opacity !== undefined ? element.opacity : 1}
      visible={element.visible !== false}
      draggable={!element.locked && !isPreviewMode}
      onClick={onClick}
      onDragEnd={onDragEnd}
    >
      {/* QR Code placeholder */}
      <Rect
        x={0}
        y={0}
        width={element.width}
        height={element.height}
        fill="#ffffff"
        stroke="#000"
        strokeWidth={1}
      />

      {/* QR pattern simulation */}
      <Rect
        x={-element.width / 2 + element.width * 0.1}
        y={-element.height / 2 + element.height * 0.1}
        width={element.width * 0.8}
        height={element.height * 0.8}
        fill="#000"
        opacity={0.8}
      />

      {/* Center text */}
      <Text
        x={-element.width / 2}
        y={-element.height / 2}
        width={element.width}
        height={element.height}
        text="QR"
        fontSize={Math.min(element.width, element.height) * 0.2}
        fill="#fff"
        align="center"
        verticalAlign="middle"
        fontWeight="bold"
      />

      {/* Data preview */}
      {isPreviewMode && (
        <Text
          x={-element.width / 2}
          y={element.height / 2 + 5}
          width={element.width}
          text={getQRData()}
          fontSize={8}
          fill="#666"
          align="center"
        />
      )}
    </Group>
  );
});

QRCodeElement.displayName = 'QRCodeElement';

export default QRCodeElement;
