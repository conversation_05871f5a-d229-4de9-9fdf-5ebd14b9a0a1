import React, { forwardRef } from 'react';
import { Text } from 'react-konva';
import { useSelector } from 'react-redux';
import Mustache from 'mustache';

const TextElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const { mockData } = useSelector((state) => state.idDesigner);

  const getDisplayText = () => {
    if (isPreviewMode && element.text) {
      try {
        // Render mustache template with mock data
        return Mustache.render(element.text, mockData);
      } catch (error) {
        console.warn('Error rendering mustache template:', error);
        return element.text;
      }
    }
    return element.text || 'Text';
  };



  // Auto-resize font based on element dimensions
  const getAutoFontSize = () => {
    if (element.autoResize !== false) {
      const baseSize = Math.min(element.width, element.height) / 4;
      return Math.max(8, Math.min(baseSize, element.fontSize || 16));
    }
    return element.fontSize || 16;
  };

  const textProps = {
    ref,
    x: element.x + element.width / 2,
    y: element.y + element.height / 2,
    width: element.width,
    height: element.height,
    text: getDisplayText(),
    fontSize: getAutoFontSize(),
    fontFamily: element.fontFamily || 'Arial',
    fontStyle: element.fontStyle || 'normal',
    fontVariant: element.fontVariant || 'normal',
    fill: element.color || '#000000',
    align: element.textAlign || 'center',
    verticalAlign: element.verticalAlign || 'middle',
    wrap: element.wrap || 'word',
    ellipsis: element.ellipsis || false,
    rotation: element.rotation || 0,
    opacity: element.opacity !== undefined ? element.opacity : 1,
    visible: element.visible !== false,
    draggable: !element.locked && !isPreviewMode,
    onClick,
    onDragEnd,
    offsetX: element.width / 2,
    offsetY: element.height / 2,
  };

  // Add text decoration styles
  if (element.textDecoration) {
    if (element.textDecoration.includes('underline')) {
      textProps.textDecoration = 'underline';
    }
    if (element.textDecoration.includes('line-through')) {
      textProps.textDecoration = 'line-through';
    }
  }

  // Add shadow if specified
  if (element.shadow) {
    textProps.shadowColor = element.shadow.color || 'rgba(0,0,0,0.5)';
    textProps.shadowBlur = element.shadow.blur || 5;
    textProps.shadowOffset = element.shadow.offset || { x: 2, y: 2 };
    textProps.shadowOpacity = element.shadow.opacity || 0.5;
  }

  // For now, we'll handle text editing through the property panel
  // In-place editing can be added later with a proper implementation

  return <Text {...textProps} />;
});

TextElement.displayName = 'TextElement';

export default TextElement;
