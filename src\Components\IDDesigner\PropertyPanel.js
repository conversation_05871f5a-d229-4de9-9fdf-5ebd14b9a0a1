import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Slider,
  Switch,
  FormControlLabel,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  IconButton,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Delete as DeleteIcon,
  ContentCopy as DuplicateIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  RotateRight as RotateIcon,
} from '@mui/icons-material';

import {
  updateElement,
  deleteElement,
  duplicateElement,
  moveElementToFront,
  moveElementToBack,
  updateCanvasConfig,
} from '../../redux/idDesignerSlice';

const PropertyPanel = () => {
  const dispatch = useDispatch();
  const { elements, selectedElementIds, canvasConfig } = useSelector((state) => state.idDesigner);

  const selectedElements = elements.filter(el => selectedElementIds.includes(el.id));
  const selectedElement = selectedElements.length === 1 ? selectedElements[0] : null;

  const handlePropertyChange = (property, value) => {
    selectedElementIds.forEach(id => {
      dispatch(updateElement({
        id,
        properties: { [property]: value }
      }));
    });
  };

  const handleDelete = () => {
    selectedElementIds.forEach(id => {
      dispatch(deleteElement(id));
    });
  };

  const handleDuplicate = () => {
    if (selectedElement) {
      dispatch(duplicateElement(selectedElement.id));
    }
  };

  const renderCanvasProperties = () => (
    <Accordion defaultExpanded={selectedElementIds.length === 0}>
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography variant="subtitle2">Canvas Settings</Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              label="Width"
              type="number"
              value={canvasConfig.width}
              onChange={(e) => dispatch(updateCanvasConfig({ width: parseInt(e.target.value) }))}
              size="small"
              sx={{ flex: 1 }}
            />
            <TextField
              label="Height"
              type="number"
              value={canvasConfig.height}
              onChange={(e) => dispatch(updateCanvasConfig({ height: parseInt(e.target.value) }))}
              size="small"
              sx={{ flex: 1 }}
            />
          </Box>

          <TextField
            label="Background Color"
            type="color"
            value={canvasConfig.background}
            onChange={(e) => dispatch(updateCanvasConfig({ background: e.target.value }))}
            size="small"
          />

          <FormControlLabel
            control={
              <Switch
                checked={canvasConfig.showGrid}
                onChange={(e) => dispatch(updateCanvasConfig({ showGrid: e.target.checked }))}
              />
            }
            label="Show Grid"
          />

          <FormControlLabel
            control={
              <Switch
                checked={canvasConfig.snapToGrid}
                onChange={(e) => dispatch(updateCanvasConfig({ snapToGrid: e.target.checked }))}
              />
            }
            label="Snap to Grid"
          />

          <Box>
            <Typography variant="caption" gutterBottom>
              Grid Size: {canvasConfig.gridSize}px
            </Typography>
            <Slider
              value={canvasConfig.gridSize}
              onChange={(e, value) => dispatch(updateCanvasConfig({ gridSize: value }))}
              min={5}
              max={50}
              size="small"
            />
          </Box>
        </Box>
      </AccordionDetails>
    </Accordion>
  );

  if (selectedElementIds.length === 0) {
    return (
      <Box sx={{ height: '100%', overflow: 'auto' }}>
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
            Canvas Properties
          </Typography>
        </Box>
        {renderCanvasProperties()}
      </Box>
    );
  }

  const renderTextProperties = () => {
    if (!selectedElement || selectedElement.type !== 'text') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Text Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Text Content"
              value={selectedElement.text || ''}
              onChange={(e) => handlePropertyChange('text', e.target.value)}
              multiline
              rows={2}
              size="small"
              helperText="Use {{variable}} for dynamic content"
            />
            
            <FormControl size="small">
              <InputLabel>Font Family</InputLabel>
              <Select
                value={selectedElement.fontFamily || 'Arial'}
                onChange={(e) => handlePropertyChange('fontFamily', e.target.value)}
                label="Font Family"
              >
                <MenuItem value="Arial">Arial</MenuItem>
                <MenuItem value="Helvetica">Helvetica</MenuItem>
                <MenuItem value="Times New Roman">Times New Roman</MenuItem>
                <MenuItem value="Courier New">Courier New</MenuItem>
                <MenuItem value="Georgia">Georgia</MenuItem>
                <MenuItem value="Verdana">Verdana</MenuItem>
              </Select>
            </FormControl>

            <Box>
              <Typography variant="caption" gutterBottom>
                Font Size: {selectedElement.fontSize || 16}px
              </Typography>
              <Slider
                value={selectedElement.fontSize || 16}
                onChange={(e, value) => handlePropertyChange('fontSize', value)}
                min={8}
                max={72}
                size="small"
              />
            </Box>

            <TextField
              label="Text Color"
              type="color"
              value={selectedElement.color || '#000000'}
              onChange={(e) => handlePropertyChange('color', e.target.value)}
              size="small"
            />

            <FormControl size="small">
              <InputLabel>Text Align</InputLabel>
              <Select
                value={selectedElement.textAlign || 'center'}
                onChange={(e) => handlePropertyChange('textAlign', e.target.value)}
                label="Text Align"
              >
                <MenuItem value="left">Left</MenuItem>
                <MenuItem value="center">Center</MenuItem>
                <MenuItem value="right">Right</MenuItem>
              </Select>
            </FormControl>

            <FormControlLabel
              control={
                <Switch
                  checked={selectedElement.autoResize !== false}
                  onChange={(e) => handlePropertyChange('autoResize', e.target.checked)}
                />
              }
              label="Auto-resize Font"
            />
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderImageProperties = () => {
    if (!selectedElement || selectedElement.type !== 'image') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Image Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Image URL"
              value={selectedElement.src || ''}
              onChange={(e) => handlePropertyChange('src', e.target.value)}
              size="small"
              helperText="Enter image URL or upload file"
            />
            
            <Button variant="outlined" size="small">
              Upload Image
            </Button>

            <FormControlLabel
              control={
                <Switch
                  checked={selectedElement.maintainAspectRatio !== false}
                  onChange={(e) => handlePropertyChange('maintainAspectRatio', e.target.checked)}
                />
              }
              label="Maintain Aspect Ratio"
            />
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderQRCodeProperties = () => {
    if (!selectedElement || selectedElement.type !== 'qrcode') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">QR Code Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="QR Code Data"
              value={selectedElement.data || ''}
              onChange={(e) => handlePropertyChange('data', e.target.value)}
              size="small"
              helperText="Use {{variable}} for dynamic content"
            />

            <FormControl size="small">
              <InputLabel>Error Correction</InputLabel>
              <Select
                value={selectedElement.errorCorrectionLevel || 'M'}
                onChange={(e) => handlePropertyChange('errorCorrectionLevel', e.target.value)}
                label="Error Correction"
              >
                <MenuItem value="L">Low (7%)</MenuItem>
                <MenuItem value="M">Medium (15%)</MenuItem>
                <MenuItem value="Q">Quartile (25%)</MenuItem>
                <MenuItem value="H">High (30%)</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderPositionProperties = () => {
    if (!selectedElement) return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Position & Size</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                label="X"
                type="number"
                value={Math.round(selectedElement.x || 0)}
                onChange={(e) => handlePropertyChange('x', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
              <TextField
                label="Y"
                type="number"
                value={Math.round(selectedElement.y || 0)}
                onChange={(e) => handlePropertyChange('y', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                label="Width"
                type="number"
                value={Math.round(selectedElement.width || 0)}
                onChange={(e) => handlePropertyChange('width', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
              <TextField
                label="Height"
                type="number"
                value={Math.round(selectedElement.height || 0)}
                onChange={(e) => handlePropertyChange('height', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
            </Box>

            <Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <RotateIcon sx={{ fontSize: 16 }} />
                <Typography variant="caption">
                  Rotation: {Math.round(selectedElement.rotation || 0)}°
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('rotation', (selectedElement.rotation || 0) + 90)}
                  title="Rotate 90°"
                >
                  <RotateIcon sx={{ fontSize: 16 }} />
                </IconButton>
              </Box>
              <Slider
                value={selectedElement.rotation || 0}
                onChange={(e, value) => handlePropertyChange('rotation', value)}
                min={-180}
                max={180}
                size="small"
              />
            </Box>

            <Box>
              <Typography variant="caption" gutterBottom>
                Opacity: {Math.round((selectedElement.opacity || 1) * 100)}%
              </Typography>
              <Slider
                value={selectedElement.opacity || 1}
                onChange={(e, value) => handlePropertyChange('opacity', value)}
                min={0}
                max={1}
                step={0.1}
                size="small"
              />
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <Box sx={{ height: '100%', overflow: 'auto' }}>
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
          Properties
        </Typography>
        {selectedElementIds.length > 1 && (
          <Typography variant="caption" color="text.secondary">
            {selectedElementIds.length} elements selected
          </Typography>
        )}
      </Box>

      {/* Element Actions */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <IconButton
            size="small"
            onClick={handleDuplicate}
            disabled={selectedElementIds.length !== 1}
            title="Duplicate"
          >
            <DuplicateIcon />
          </IconButton>
          
          <IconButton
            size="small"
            onClick={handleDelete}
            color="error"
            title="Delete"
          >
            <DeleteIcon />
          </IconButton>

          <IconButton
            size="small"
            onClick={() => handlePropertyChange('locked', !selectedElement?.locked)}
            title={selectedElement?.locked ? 'Unlock' : 'Lock'}
          >
            {selectedElement?.locked ? <LockIcon /> : <UnlockIcon />}
          </IconButton>

          <IconButton
            size="small"
            onClick={() => handlePropertyChange('visible', !selectedElement?.visible)}
            title={selectedElement?.visible ? 'Hide' : 'Show'}
          >
            {selectedElement?.visible !== false ? <VisibilityIcon /> : <VisibilityOffIcon />}
          </IconButton>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
          <Button
            size="small"
            variant="outlined"
            onClick={() => selectedElement && dispatch(moveElementToFront(selectedElement.id))}
            disabled={!selectedElement}
          >
            To Front
          </Button>
          <Button
            size="small"
            variant="outlined"
            onClick={() => selectedElement && dispatch(moveElementToBack(selectedElement.id))}
            disabled={!selectedElement}
          >
            To Back
          </Button>
        </Box>
      </Box>

      {/* Dynamic Properties */}
      <Box>
        {renderPositionProperties()}
        {renderTextProperties()}
        {renderImageProperties()}
        {renderQRCodeProperties()}
        {renderCanvasProperties()}
      </Box>
    </Box>
  );
};

export default PropertyPanel;
