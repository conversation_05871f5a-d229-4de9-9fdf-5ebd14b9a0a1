import React from 'react';
import { Box } from '@mui/material';

const SelectionBox = ({ elements, canvasConfig }) => {
  if (!elements || elements.length === 0) return null;

  // Calculate bounding box for multiple selected elements
  const bounds = elements.reduce((acc, element) => {
    const left = element.x * canvasConfig.scale + canvasConfig.offsetX;
    const top = element.y * canvasConfig.scale + canvasConfig.offsetY;
    const right = left + element.width * canvasConfig.scale;
    const bottom = top + element.height * canvasConfig.scale;

    return {
      left: Math.min(acc.left, left),
      top: Math.min(acc.top, top),
      right: Math.max(acc.right, right),
      bottom: Math.max(acc.bottom, bottom),
    };
  }, {
    left: Infinity,
    top: Infinity,
    right: -Infinity,
    bottom: -Infinity,
  });

  const width = bounds.right - bounds.left;
  const height = bounds.bottom - bounds.top;

  return (
    <Box
      sx={{
        position: 'absolute',
        left: bounds.left,
        top: bounds.top,
        width,
        height,
        border: '2px dashed #0066cc',
        backgroundColor: 'rgba(0, 102, 204, 0.1)',
        pointerEvents: 'none',
        zIndex: 1000,
      }}
    />
  );
};

export default SelectionBox;
