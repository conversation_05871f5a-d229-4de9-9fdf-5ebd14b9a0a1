/**
 * Unit conversion utilities for the ID card designer
 * Handles conversion between pixels, inches, and millimeters
 */

// Standard DPI for print design (300 DPI is common for high-quality printing)
export const DEFAULT_DPI = 300;

// Unit types
export const UNITS = {
  INCHES: 'inches',
  MILLIMETERS: 'mm',
  PIXELS: 'px'
};

// Conversion constants
export const MM_PER_INCH = 25.4;

/**
 * Convert pixels to inches
 * @param {number} pixels - Value in pixels
 * @param {number} dpi - Dots per inch (default: 300)
 * @returns {number} Value in inches
 */
export const pixelsToInches = (pixels, dpi = DEFAULT_DPI) => {
  return pixels / dpi;
};

/**
 * Convert inches to pixels
 * @param {number} inches - Value in inches
 * @param {number} dpi - Dots per inch (default: 300)
 * @returns {number} Value in pixels
 */
export const inchesToPixels = (inches, dpi = DEFAULT_DPI) => {
  return inches * dpi;
};

/**
 * Convert pixels to millimeters
 * @param {number} pixels - Value in pixels
 * @param {number} dpi - Dots per inch (default: 300)
 * @returns {number} Value in millimeters
 */
export const pixelsToMm = (pixels, dpi = DEFAULT_DPI) => {
  const inches = pixelsToInches(pixels, dpi);
  return inches * MM_PER_INCH;
};

/**
 * Convert millimeters to pixels
 * @param {number} mm - Value in millimeters
 * @param {number} dpi - Dots per inch (default: 300)
 * @returns {number} Value in pixels
 */
export const mmToPixels = (mm, dpi = DEFAULT_DPI) => {
  const inches = mm / MM_PER_INCH;
  return inchesToPixels(inches, dpi);
};

/**
 * Convert inches to millimeters
 * @param {number} inches - Value in inches
 * @returns {number} Value in millimeters
 */
export const inchesToMm = (inches) => {
  return inches * MM_PER_INCH;
};

/**
 * Convert millimeters to inches
 * @param {number} mm - Value in millimeters
 * @returns {number} Value in inches
 */
export const mmToInches = (mm) => {
  return mm / MM_PER_INCH;
};

/**
 * Convert from any unit to pixels
 * @param {number} value - Value to convert
 * @param {string} fromUnit - Source unit (inches, mm, px)
 * @param {number} dpi - Dots per inch (default: 300)
 * @returns {number} Value in pixels
 */
export const toPixels = (value, fromUnit, dpi = DEFAULT_DPI) => {
  switch (fromUnit) {
    case UNITS.INCHES:
      return inchesToPixels(value, dpi);
    case UNITS.MILLIMETERS:
      return mmToPixels(value, dpi);
    case UNITS.PIXELS:
      return value;
    default:
      throw new Error(`Unknown unit: ${fromUnit}`);
  }
};

/**
 * Convert from pixels to any unit
 * @param {number} pixels - Value in pixels
 * @param {string} toUnit - Target unit (inches, mm, px)
 * @param {number} dpi - Dots per inch (default: 300)
 * @returns {number} Value in target unit
 */
export const fromPixels = (pixels, toUnit, dpi = DEFAULT_DPI) => {
  switch (toUnit) {
    case UNITS.INCHES:
      return pixelsToInches(pixels, dpi);
    case UNITS.MILLIMETERS:
      return pixelsToMm(pixels, dpi);
    case UNITS.PIXELS:
      return pixels;
    default:
      throw new Error(`Unknown unit: ${toUnit}`);
  }
};

/**
 * Convert between any two units
 * @param {number} value - Value to convert
 * @param {string} fromUnit - Source unit
 * @param {string} toUnit - Target unit
 * @param {number} dpi - Dots per inch (default: 300)
 * @returns {number} Converted value
 */
export const convertUnit = (value, fromUnit, toUnit, dpi = DEFAULT_DPI) => {
  if (fromUnit === toUnit) return value;
  
  const pixels = toPixels(value, fromUnit, dpi);
  return fromPixels(pixels, toUnit, dpi);
};

/**
 * Format a value with unit label
 * @param {number} value - Numeric value
 * @param {string} unit - Unit type
 * @param {number} precision - Decimal places (default: 2)
 * @returns {string} Formatted string with unit
 */
export const formatWithUnit = (value, unit, precision = 2) => {
  const roundedValue = Number(value.toFixed(precision));
  const unitLabel = unit === UNITS.MILLIMETERS ? 'mm' : 
                   unit === UNITS.INCHES ? '"' : 'px';
  return `${roundedValue}${unitLabel}`;
};

/**
 * Get grid size in pixels for the current unit
 * @param {string} unit - Current unit
 * @param {number} dpi - Dots per inch
 * @returns {number} Grid size in pixels
 */
export const getGridSizeInPixels = (unit, dpi = DEFAULT_DPI) => {
  switch (unit) {
    case UNITS.INCHES:
      return inchesToPixels(0.1, dpi); // 0.1 inch grid
    case UNITS.MILLIMETERS:
      return mmToPixels(2.5, dpi); // 2.5mm grid (approximately 0.1 inch)
    default:
      return 10; // 10 pixel grid
  }
};

/**
 * Standard ID card dimensions in different units
 */
export const STANDARD_CARD_SIZES = {
  'CR80 (Standard)': {
    width: { inches: 3.375, mm: 85.725 },
    height: { inches: 2.125, mm: 53.975 }
  },
  'CR79 (Government)': {
    width: { inches: 3.303, mm: 83.896 },
    height: { inches: 2.051, mm: 52.096 }
  },
  'Custom': {
    width: { inches: 4, mm: 101.6 },
    height: { inches: 3, mm: 76.2 }
  }
};

/**
 * Get standard card size in pixels
 * @param {string} cardType - Card type key
 * @param {string} unit - Unit to convert from
 * @param {number} dpi - Dots per inch
 * @returns {object} Width and height in pixels
 */
export const getCardSizeInPixels = (cardType, unit = UNITS.INCHES, dpi = DEFAULT_DPI) => {
  const cardSize = STANDARD_CARD_SIZES[cardType];
  if (!cardSize) return null;
  
  const width = toPixels(cardSize.width[unit], unit, dpi);
  const height = toPixels(cardSize.height[unit], unit, dpi);
  
  return { width, height };
};
